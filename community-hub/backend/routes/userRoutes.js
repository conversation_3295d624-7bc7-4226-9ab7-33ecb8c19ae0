/**
 * User Routes for Barber Brothers Community Hub
 * Handles user profiles, following, and user management
 */

const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const authMiddleware = require('../middleware/auth');
const uploadMiddleware = require('../middleware/upload');
const { body, query } = require('express-validator');

// Validation middleware
const updateProfileValidation = [
    body('name')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Name must be between 2 and 50 characters'),
    body('bio')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Bio must be less than 500 characters'),
    body('location')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Location must be less than 100 characters'),
    body('website')
        .optional()
        .isURL()
        .withMessage('Website must be a valid URL'),
    body('profession')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Profession must be less than 100 characters')
];

const paginationValidation = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    query('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('Limit must be between 1 and 50')
];

// @route   GET /api/users
// @desc    Get all users with pagination and search
// @access  Public
router.get('/', [
    query('search')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Search query must be between 1 and 50 characters'),
    ...paginationValidation
], userController.getUsers);

// @route   GET /api/users/search
// @desc    Search users
// @access  Public
router.get('/search', [
    query('q')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Search query must be between 1 and 50 characters'),
    ...paginationValidation
], userController.searchUsers);

// @route   GET /api/users/suggested
// @desc    Get suggested users to follow
// @access  Private
router.get('/suggested', authMiddleware, paginationValidation, userController.getSuggestedUsers);

// @route   GET /api/users/barbers
// @desc    Get verified barbers
// @access  Public
router.get('/barbers', paginationValidation, userController.getBarbers);

// @route   GET /api/users/:id
// @desc    Get user profile by ID
// @access  Public
router.get('/:id', userController.getUserProfile);

// @route   PUT /api/users/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', authMiddleware, updateProfileValidation, userController.updateProfile);

// @route   POST /api/users/avatar
// @desc    Upload user avatar
// @access  Private
router.post('/avatar', authMiddleware, uploadMiddleware.single('avatar'), userController.uploadAvatar);

// @route   POST /api/users/cover
// @desc    Upload user cover photo
// @access  Private
router.post('/cover', authMiddleware, uploadMiddleware.single('cover'), userController.uploadCoverPhoto);

// @route   DELETE /api/users/avatar
// @desc    Delete user avatar
// @access  Private
router.delete('/avatar', authMiddleware, userController.deleteAvatar);

// @route   DELETE /api/users/cover
// @desc    Delete user cover photo
// @access  Private
router.delete('/cover', authMiddleware, userController.deleteCoverPhoto);

// @route   POST /api/users/:id/follow
// @desc    Follow/unfollow a user
// @access  Private
router.post('/:id/follow', authMiddleware, userController.toggleFollow);

// @route   GET /api/users/:id/followers
// @desc    Get user followers
// @access  Public
router.get('/:id/followers', paginationValidation, userController.getFollowers);

// @route   GET /api/users/:id/following
// @desc    Get users that this user is following
// @access  Public
router.get('/:id/following', paginationValidation, userController.getFollowing);

// @route   GET /api/users/:id/posts
// @desc    Get posts by user
// @access  Public
router.get('/:id/posts', paginationValidation, userController.getUserPosts);

// @route   GET /api/users/:id/stats
// @desc    Get user statistics
// @access  Public
router.get('/:id/stats', userController.getUserStats);

// @route   POST /api/users/:id/block
// @desc    Block/unblock a user
// @access  Private
router.post('/:id/block', authMiddleware, userController.toggleBlock);

// @route   GET /api/users/blocked/me
// @desc    Get blocked users
// @access  Private
router.get('/blocked/me', authMiddleware, paginationValidation, userController.getBlockedUsers);

// @route   POST /api/users/:id/report
// @desc    Report a user
// @access  Private
router.post('/:id/report', authMiddleware, [
    body('reason')
        .trim()
        .isLength({ min: 1, max: 500 })
        .withMessage('Report reason must be between 1 and 500 characters'),
    body('category')
        .isIn(['spam', 'harassment', 'inappropriate', 'fake_account', 'other'])
        .withMessage('Invalid report category')
], userController.reportUser);

// @route   PUT /api/users/privacy
// @desc    Update privacy settings
// @access  Private
router.put('/privacy', authMiddleware, [
    body('profileVisibility')
        .optional()
        .isIn(['public', 'private'])
        .withMessage('Profile visibility must be public or private'),
    body('showEmail')
        .optional()
        .isBoolean()
        .withMessage('Show email must be a boolean'),
    body('allowMessages')
        .optional()
        .isBoolean()
        .withMessage('Allow messages must be a boolean')
], userController.updatePrivacySettings);

module.exports = router;
