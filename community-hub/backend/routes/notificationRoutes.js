/**
 * Notification Routes for Barber Brothers Community Hub
 * Handles notification retrieval, marking as read, and preferences
 */

const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const authMiddleware = require('../middleware/auth');
const { body, query } = require('express-validator');

// Validation middleware
const paginationValidation = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    query('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('Limit must be between 1 and 50')
];

const notificationPreferencesValidation = [
    body('emailNotifications')
        .optional()
        .isBoolean()
        .withMessage('Email notifications must be a boolean'),
    body('pushNotifications')
        .optional()
        .isBoolean()
        .withMessage('Push notifications must be a boolean'),
    body('likeNotifications')
        .optional()
        .isBoolean()
        .withMessage('Like notifications must be a boolean'),
    body('commentNotifications')
        .optional()
        .isBoolean()
        .withMessage('Comment notifications must be a boolean'),
    body('followNotifications')
        .optional()
        .isBoolean()
        .withMessage('Follow notifications must be a boolean'),
    body('mentionNotifications')
        .optional()
        .isBoolean()
        .withMessage('Mention notifications must be a boolean')
];

// @route   GET /api/notifications
// @desc    Get user notifications
// @access  Private
router.get('/', authMiddleware, [
    query('type')
        .optional()
        .isIn(['like', 'comment', 'follow', 'mention', 'post', 'system'])
        .withMessage('Invalid notification type'),
    query('read')
        .optional()
        .isBoolean()
        .withMessage('Read filter must be a boolean'),
    ...paginationValidation
], notificationController.getNotifications);

// @route   GET /api/notifications/unread-count
// @desc    Get unread notification count
// @access  Private
router.get('/unread-count', authMiddleware, notificationController.getUnreadCount);

// @route   PUT /api/notifications/:id/read
// @desc    Mark notification as read
// @access  Private
router.put('/:id/read', authMiddleware, notificationController.markAsRead);

// @route   PUT /api/notifications/mark-all-read
// @desc    Mark all notifications as read
// @access  Private
router.put('/mark-all-read', authMiddleware, notificationController.markAllAsRead);

// @route   DELETE /api/notifications/:id
// @desc    Delete a notification
// @access  Private
router.delete('/:id', authMiddleware, notificationController.deleteNotification);

// @route   DELETE /api/notifications/clear-all
// @desc    Clear all notifications
// @access  Private
router.delete('/clear-all', authMiddleware, notificationController.clearAllNotifications);

// @route   GET /api/notifications/preferences
// @desc    Get notification preferences
// @access  Private
router.get('/preferences', authMiddleware, notificationController.getPreferences);

// @route   PUT /api/notifications/preferences
// @desc    Update notification preferences
// @access  Private
router.put('/preferences', authMiddleware, notificationPreferencesValidation, notificationController.updatePreferences);

// @route   POST /api/notifications/test
// @desc    Send test notification (development only)
// @access  Private
router.post('/test', authMiddleware, [
    body('type')
        .isIn(['like', 'comment', 'follow', 'mention', 'post', 'system'])
        .withMessage('Invalid notification type'),
    body('message')
        .optional()
        .trim()
        .isLength({ min: 1, max: 200 })
        .withMessage('Message must be between 1 and 200 characters')
], notificationController.sendTestNotification);

// @route   GET /api/notifications/recent
// @desc    Get recent notifications (last 24 hours)
// @access  Private
router.get('/recent', authMiddleware, notificationController.getRecentNotifications);

// @route   GET /api/notifications/summary
// @desc    Get notification summary
// @access  Private
router.get('/summary', authMiddleware, notificationController.getNotificationSummary);

module.exports = router;
