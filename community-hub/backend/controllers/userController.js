/**
 * User Controller
 * Handles user profile management, following system, and user interactions
 */

const User = require('../models/User');
const Post = require('../models/Post');
const Notification = require('../models/Notification');
const { validationResult } = require('express-validator');
const cloudinary = require('cloudinary').v2;

class UserController {
    /**
     * Get user profile by ID
     */
    static async getUserProfile(req, res) {
        try {
            const { userId } = req.params;
            const currentUser = req.user;

            const user = await User.findOne({
                _id: userId,
                isActive: true,
                isDeleted: false
            }).select('-password -refreshTokens -emailVerificationToken -passwordResetToken');

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            // Check if current user is following this user
            let isFollowing = false;
            let isFollowedBy = false;
            
            if (currentUser) {
                isFollowing = user.followers.some(follower => 
                    follower.user.toString() === currentUser._id.toString()
                );
                isFollowedBy = user.following.some(following => 
                    following.user.toString() === currentUser._id.toString()
                );
            }

            // Get user's recent posts count
            const postsCount = await Post.countDocuments({
                author: userId,
                isActive: true,
                isDeleted: false,
                visibility: { $in: ['public', 'followers'] }
            });

            const userProfile = {
                ...user.toObject(),
                postsCount,
                followersCount: user.followers.length,
                followingCount: user.following.length,
                isFollowing,
                isFollowedBy,
                isSelf: currentUser ? currentUser._id.toString() === userId : false
            };

            res.json({
                success: true,
                data: { user: userProfile }
            });

        } catch (error) {
            console.error('❌ Get user profile error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get user profile',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Update user profile
     */
    static async updateProfile(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const userId = req.user._id;
            const updates = req.body;

            // Remove fields that shouldn't be updated via this endpoint
            delete updates.email;
            delete updates.password;
            delete updates.role;
            delete updates.isVerified;
            delete updates.followers;
            delete updates.following;

            const user = await User.findByIdAndUpdate(
                userId,
                { ...updates, updatedAt: Date.now() },
                { new: true, runValidators: true }
            ).select('-password -refreshTokens -emailVerificationToken -passwordResetToken');

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            res.json({
                success: true,
                message: 'Profile updated successfully',
                data: { user }
            });

        } catch (error) {
            console.error('❌ Update profile error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update profile',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Follow/Unfollow user
     */
    static async toggleFollow(req, res) {
        try {
            const { userId } = req.params;
            const currentUserId = req.user._id;

            if (userId === currentUserId.toString()) {
                return res.status(400).json({
                    success: false,
                    message: 'Cannot follow yourself'
                });
            }

            const [targetUser, currentUser] = await Promise.all([
                User.findOne({ _id: userId, isActive: true, isDeleted: false }),
                User.findById(currentUserId)
            ]);

            if (!targetUser) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            // Check if already following
            const isFollowing = targetUser.followers.some(follower => 
                follower.user.toString() === currentUserId.toString()
            );

            let action;
            if (isFollowing) {
                // Unfollow
                targetUser.followers = targetUser.followers.filter(follower => 
                    follower.user.toString() !== currentUserId.toString()
                );
                currentUser.following = currentUser.following.filter(following => 
                    following.user.toString() !== userId
                );
                action = 'unfollowed';
            } else {
                // Follow
                targetUser.followers.push({ user: currentUserId });
                currentUser.following.push({ user: userId });
                action = 'followed';

                // Create notification
                await Notification.createFollowNotification(currentUserId, userId);
            }

            await Promise.all([targetUser.save(), currentUser.save()]);

            res.json({
                success: true,
                message: `User ${action} successfully`,
                data: {
                    action,
                    followersCount: targetUser.followers.length,
                    followingCount: currentUser.following.length
                }
            });

        } catch (error) {
            console.error('❌ Toggle follow error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update follow status',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Get user's followers
     */
    static async getFollowers(req, res) {
        try {
            const { userId } = req.params;
            const { page = 1, limit = 20 } = req.query;

            const user = await User.findOne({
                _id: userId,
                isActive: true,
                isDeleted: false
            }).populate({
                path: 'followers.user',
                select: 'username displayName avatar isVerified',
                options: {
                    skip: (page - 1) * limit,
                    limit: parseInt(limit)
                }
            });

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            const followers = user.followers.map(f => f.user);
            const totalFollowers = user.followers.length;

            res.json({
                success: true,
                data: {
                    followers,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total: totalFollowers,
                        pages: Math.ceil(totalFollowers / limit)
                    }
                }
            });

        } catch (error) {
            console.error('❌ Get followers error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get followers',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Get user's following
     */
    static async getFollowing(req, res) {
        try {
            const { userId } = req.params;
            const { page = 1, limit = 20 } = req.query;

            const user = await User.findOne({
                _id: userId,
                isActive: true,
                isDeleted: false
            }).populate({
                path: 'following.user',
                select: 'username displayName avatar isVerified',
                options: {
                    skip: (page - 1) * limit,
                    limit: parseInt(limit)
                }
            });

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            const following = user.following.map(f => f.user);
            const totalFollowing = user.following.length;

            res.json({
                success: true,
                data: {
                    following,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total: totalFollowing,
                        pages: Math.ceil(totalFollowing / limit)
                    }
                }
            });

        } catch (error) {
            console.error('❌ Get following error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get following',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Upload user avatar
     */
    static async uploadAvatar(req, res) {
        try {
            if (!req.file) {
                return res.status(400).json({
                    success: false,
                    message: 'No image file provided'
                });
            }

            const userId = req.user._id;
            const user = await User.findById(userId);

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            // Delete old avatar if exists
            if (user.avatar && user.avatar.publicId) {
                try {
                    await cloudinary.uploader.destroy(user.avatar.publicId);
                } catch (error) {
                    console.warn('Failed to delete old avatar:', error);
                }
            }

            // Upload new avatar
            const result = await cloudinary.uploader.upload(req.file.path, {
                folder: 'barber-brothers/avatars',
                width: 300,
                height: 300,
                crop: 'fill',
                gravity: 'face'
            });

            user.avatar = {
                url: result.secure_url,
                publicId: result.public_id
            };

            await user.save();

            res.json({
                success: true,
                message: 'Avatar uploaded successfully',
                data: { avatar: user.avatar }
            });

        } catch (error) {
            console.error('❌ Upload avatar error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to upload avatar',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Upload user cover photo
     */
    static async uploadCoverPhoto(req, res) {
        try {
            if (!req.file) {
                return res.status(400).json({
                    success: false,
                    message: 'No image file provided'
                });
            }

            const userId = req.user._id;
            const user = await User.findById(userId);

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            // Delete old cover photo if exists
            if (user.coverPhoto && user.coverPhoto.publicId) {
                try {
                    await cloudinary.uploader.destroy(user.coverPhoto.publicId);
                } catch (error) {
                    console.warn('Failed to delete old cover photo:', error);
                }
            }

            // Upload new cover photo
            const result = await cloudinary.uploader.upload(req.file.path, {
                folder: 'barber-brothers/covers',
                width: 1200,
                height: 400,
                crop: 'fill'
            });

            user.coverPhoto = {
                url: result.secure_url,
                publicId: result.public_id
            };

            await user.save();

            res.json({
                success: true,
                message: 'Cover photo uploaded successfully',
                data: { coverPhoto: user.coverPhoto }
            });

        } catch (error) {
            console.error('❌ Upload cover photo error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to upload cover photo',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Delete user avatar
     */
    static async deleteAvatar(req, res) {
        try {
            const userId = req.user._id;
            const user = await User.findById(userId);

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            if (user.avatar && user.avatar.publicId) {
                try {
                    await cloudinary.uploader.destroy(user.avatar.publicId);
                } catch (error) {
                    console.warn('Failed to delete avatar from cloudinary:', error);
                }
            }

            user.avatar = undefined;
            await user.save();

            res.json({
                success: true,
                message: 'Avatar deleted successfully'
            });

        } catch (error) {
            console.error('❌ Delete avatar error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete avatar',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Delete user cover photo
     */
    static async deleteCoverPhoto(req, res) {
        try {
            const userId = req.user._id;
            const user = await User.findById(userId);

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            if (user.coverPhoto && user.coverPhoto.publicId) {
                try {
                    await cloudinary.uploader.destroy(user.coverPhoto.publicId);
                } catch (error) {
                    console.warn('Failed to delete cover photo from cloudinary:', error);
                }
            }

            user.coverPhoto = undefined;
            await user.save();

            res.json({
                success: true,
                message: 'Cover photo deleted successfully'
            });

        } catch (error) {
            console.error('❌ Delete cover photo error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete cover photo',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Search users
     */
    static async searchUsers(req, res) {
        try {
            const { q, page = 1, limit = 20 } = req.query;

            if (!q || q.trim().length < 2) {
                return res.status(400).json({
                    success: false,
                    message: 'Search query must be at least 2 characters long'
                });
            }

            const skip = (page - 1) * limit;
            const searchRegex = new RegExp(q.trim(), 'i');

            const users = await User.find({
                $and: [
                    {
                        $or: [
                            { username: searchRegex },
                            { displayName: searchRegex },
                            { firstName: searchRegex },
                            { lastName: searchRegex }
                        ]
                    },
                    { isActive: true },
                    { isDeleted: false }
                ]
            })
            .select('username displayName firstName lastName avatar isVerified role')
            .skip(skip)
            .limit(parseInt(limit))
            .sort({ isVerified: -1, followersCount: -1 });

            const total = await User.countDocuments({
                $and: [
                    {
                        $or: [
                            { username: searchRegex },
                            { displayName: searchRegex },
                            { firstName: searchRegex },
                            { lastName: searchRegex }
                        ]
                    },
                    { isActive: true },
                    { isDeleted: false }
                ]
            });

            res.json({
                success: true,
                data: {
                    users,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        pages: Math.ceil(total / limit)
                    }
                }
            });

        } catch (error) {
            console.error('❌ Search users error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to search users',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }
}

module.exports = UserController;
